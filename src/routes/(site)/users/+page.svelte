<!-- +page.svelte - Enhanced Real-time with Better Change Detection -->
<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { get } from 'svelte/store';
	import { onMount, onDestroy } from 'svelte';

	import {
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Table,
		Button,
		Dropdown,
		Checkbox,
		Spinner,
		Indicator,
		Avatar,
		Badge,
		Input,
		Tooltip,
		Label,
	} from 'flowbite-svelte';
	import {
		EditSolid,
		ChevronDownOutline,
		UserHeadsetSolid,
		AdjustmentsHorizontalSolid,
		UserCircleSolid,
		SearchOutline,
		ClockOutline,
		CircleMinusOutline
	} from 'flowbite-svelte-icons';
	import { getColorClass } from '$lib/utils';

	import { CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';
	import { formatTimestamp, displayDate, timeAgo, getUserWorkSchedule } from '$lib/utils';
	import type { PageData } from './$types';
	import UserSignUp from '$lib/components/UI/UserSignUp.svelte';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import AdminStatistics from '$src/lib/components/admin/AdminStatistics.svelte';
	import { UserService } from '$lib/api/features/user/users.service';
	// import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
	import UserDetailSidebar from '$lib/components/UI/UserDetailSidebar.svelte';
	import { PollingService } from '$lib/services/pollingService';
	import type { UserResponse } from '$lib/api/types/user';

	export let data: PageData;
	$: ({
		// users: initialUsers,
		all_users,
		statuses,
		roles,
		partners,
		departments,
		error,
		all_tags,
		token
	} = data);

	// Add this reactive statement to see when all_users changes
	// $: {
	// 	console.log('ALL USERS (reactive):', all_users);
	// }

	interface User {
		id: number;
		roles: string;
		partners: string;
		username: string;
		email: string;
		status: string;
		is_active: boolean;
		name: string;
		employee_id: number;
		current_workload: number;
		last_active: string;
	}

	// ============== REAL-TIME UPDATE VARIABLES ==============
	let isRealTimeEnabled = true;
	let lastUpdateTime = new Date();
	let hasNewUpdates = false;
	let updateIndicatorTimeout: ReturnType<typeof setTimeout> | null = null;
	let connectionStatus = 'connected';
	let updateCounter = 0; // Track number of updates
	let userDataSnapshot = new Map(); // Keep for compatibility with existing features
	let debugMode = false; // Enable for debugging
	let userInteractionTimeout: ReturnType<typeof setTimeout> | null = null;

	// ============== SHARED POLLING SERVICE VARIABLES ==============
	const pollingService = PollingService.getInstance();
	let isUsersPollingEnabled = false;

	// ============== FILTER VARIABLES ==============
	let selectedStatuses = new Set(['All']);
	let selectedRoles = new Set(['All']);
	let selectedPartners = new Set(['All']);
	let selectedDepartments = new Set(['All']);
	let selectedSpecializedTags = new Set(['All']);
	let selectedActiveStatus = 'All';

	// Available options
	const statusOptions = ['All', 'online', 'busy', 'away', 'offline'];
	const roleOptions = ['All', 'Admin', 'System', 'Supervisor', 'Agent'];
	const activeStatusOptions = [t('filter_all'), t('active'), t('inactive')];

	$: partnerOptions = [
		{ name: t('filter_all'), code: 'All' },
		...Array.from(
			new Map(
				(partners || [])
					.filter(
						(partner: any) =>
							partner.name && partner.name !== '' && partner.name.toLowerCase() !== 'all'
					)
					.map((partner: any) => [
						`${partner.name}-${partner.code}`,
						{ name: partner.name, code: partner.code }
					])
			).values()
		).sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: departmentOptions = [
		{ name: t('filter_all'), code: 'All' },
		...Array.from(
			new Map(
				(departments || [])
					.filter((dept: any) => dept.name && dept.name !== '')
					.map((dept: any) => [`${dept.name}-${dept.code}`, { name: dept.name, code: dept.code }])
			).values()
		).sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: specializedTagOptions = [
		t('filter_all'),
		...Array.from(
			new Set(
				(all_tags || []).map((tag: any) => tag.name).filter((name: string) => name && name !== '')
			)
		).sort((a: any, b: any) => String(a).localeCompare(String(b)))
	];

	$: partnerCounts = {} as Record<string, number>;
	$: departmentCounts = {} as Record<string, number>;
	$: specializedTagCounts = {} as Record<string, number>;

	$: {
		partnerCounts = {} as Record<string, number>;
		departmentCounts = {} as Record<string, number>;
		specializedTagCounts = {} as Record<string, number>;

		for (const user of users) {
			for (const partner of user.partners || []) {
				const name = (partner as any).name;
				if (name !== '' && name !== 'all') {
					partnerCounts[name] = (partnerCounts[name] || 0) + 1;
				}
			}

			for (const dept of user.departments || []) {
				const name = (dept as any).name;
				if (name !== '' && name !== 'all') {
					departmentCounts[name] = (departmentCounts[name] || 0) + 1;
				}
			}

			for (const tag of user.user_tags || []) {
				const name = (tag as any).name;
				if (name !== '' && name !== 'all') {
					specializedTagCounts[name] = (specializedTagCounts[name] || 0) + 1;
				}
			}
		}
	}

	// ============== DATA VARIABLES ==============
	let users: any[] = [];
	const userService = new UserService();
	let isLoading = false;

	// ============== PAGINATION VARIABLES ==============
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// ============== SORTING VARIABLES ==============
	let sortColumn = 'id';
	let sortDirection = 'asc';
	let currentOrdering = 'id';

	// ============== SEARCH VARIABLES ==============
	let searchQuery = '';
	let searchTimeout: ReturnType<typeof setTimeout>;

	// ============== SIDEBAR VARIABLES ==============
	let isSidebarOpen = false;
	let selectedUserId: number | null = null;
	let isSidebarLoading = false;

	// ============== SERVER-SIDE ORDERING MAPPING ==============
	const columnFieldMap = {
		id: 'id',
		status: 'status',
		workload: 'current_workload',
		name: 'first_name',
		last_active: 'last_active'
	};

	// ============== REAL-TIME FUNCTIONS (Using Shared Service) ==============

	// Real-time fetch is now handled by shared polling service

	// Quick change detection is now handled by shared polling service

	// Legacy snapshot functionality kept for compatibility with existing features
	function createOptimizedSnapshot() {
		userDataSnapshot.clear();
		for (const user of users) {
			// Store only essential data with computed checksum
			const checksum = `${user.status}-${user.current_workload}-${user.last_active}-${user.is_active}`;
			userDataSnapshot.set(user.id, {
				checksum,
				status: user.status,
				current_workload: user.current_workload,
				last_active: user.last_active,
				is_active: user.is_active
			});
		}
		
		// Clean up snapshots for users that no longer exist
		const currentUserIds = new Set(users.map(u => u.id));
		for (const [userId] of userDataSnapshot) {
			if (!currentUserIds.has(userId)) {
				userDataSnapshot.delete(userId);
			}
		}
	}

	function pauseRealTimeTemporarily() {
		if (!isRealTimeEnabled) return;
		
		// Use shared service pause functionality
		pollingService.setPaused(true);
		
		if (userInteractionTimeout) {
			clearTimeout(userInteractionTimeout);
		}
		
		userInteractionTimeout = setTimeout(() => {
			pollingService.setPaused(false);
		}, 15000);
	}

	// ============== DATA FETCHING FUNCTIONS ==============
	async function fetchUsers(ordering = 'id', isRealTimeCall = false) {
		if (isLoading && !isRealTimeCall) return;
		
		if (!isRealTimeCall) {
			isLoading = true;
		}

		const statusFilters = Array.from(selectedStatuses).filter((s) => s !== 'All');
		const roleFilters = Array.from(selectedRoles).filter((r) => r !== 'All');
		const partnerFilters = Array.from(selectedPartners).filter((p) => p !== 'All');
		const departmentFilters = Array.from(selectedDepartments).filter((d) => d !== 'All');
		const specializedTagFilters = Array.from(selectedSpecializedTags).filter((t) => t !== 'All');

		// Map frontend active status values to backend expected values
		const userStatusFilter = selectedActiveStatus === 'All' ? 'all'
			: selectedActiveStatus === 'Active' ? 'active'
			: selectedActiveStatus === 'Inactive' ? 'inactive'
			: 'all';

		const filters = {
			search: searchQuery.trim() || '',
			status: statusFilters.join(','),
			role: roleFilters.join(','),
			partner: partnerFilters.join(','),
			department: departmentFilters.join(','),
			user_tag: specializedTagFilters.join(','),
			user_status: userStatusFilter,
			page: currentPage,
			page_size: itemsPerPage,
			// Add timestamp to prevent caching issues
			_t: Date.now()
		};

		try {
			const response = await userService.getUsersWithFiltersAndOrdering(token || '', filters, ordering);
			if (response.res_status === 200) {
				if ((response.users as any)?.results) {
					users = (response.users as any).results;
					console.log(response);
					const backendPageSize = 10;
					const totalCount = response.total_count || 0;
					totalPages = Math.ceil(totalCount / backendPageSize);
					totalItems = totalCount;
				} else {
					users = response.users || [];
					totalItems = users.length;
					totalPages = 1;
				}
				
				// Update optimized snapshot on successful fetch
				if (isRealTimeCall) {
					createOptimizedSnapshot();
				}
			} else {
				console.error('Failed to fetch users:', response.error_msg);
				users = [];
				totalItems = 0;
				totalPages = 1;
			}
		} catch (error: any) {
			console.error('Failed to fetch users:', error?.message || error);
			users = [];
			totalItems = 0;
			totalPages = 1;
			
			if (isRealTimeCall) {
				throw error;
			}
		}

		if (!isRealTimeCall) {
			isLoading = false;
		}
	}

	// ============== FILTER FUNCTIONS ==============
	function toggleStatus(status: string) {
		const newSet = new Set(selectedStatuses);

		if (status === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(status)) {
				newSet.delete(status);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(status);
			}
		}

		selectedStatuses = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleRole(role: string) {
		const newSet = new Set(selectedRoles);

		if (role === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(role)) {
				newSet.delete(role);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(role);
			}
		}

		selectedRoles = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function togglePartner(partner: { name: string; code: string }) {
		const newSet = new Set(selectedPartners);
		const partnerName = partner.name;

		if (partnerName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(partnerName)) {
				newSet.delete(partnerName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(partnerName);
			}
		}

		selectedPartners = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleDepartment(department: { name: string; code: string }) {
		const newSet = new Set(selectedDepartments);
		const departmentName = department.name;

		if (departmentName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(departmentName)) {
				newSet.delete(departmentName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(departmentName);
			}
		}

		selectedDepartments = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleSpecializedTag(tag: string) {
		const newSet = new Set(selectedSpecializedTags);

		if (tag === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tag)) {
				newSet.delete(tag);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tag);
			}
		}

		selectedSpecializedTags = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function resetFilters() {
		selectedStatuses = new Set(['All']);
		selectedRoles = new Set(['All']);
		selectedPartners = new Set(['All']);
		selectedDepartments = new Set(['All']);
		selectedSpecializedTags = new Set(['All']);
		selectedActiveStatus = 'All';
		sortColumn = 'id';
		sortDirection = 'asc';
		searchQuery = '';
		currentPage = 1;
		currentOrdering = 'id';
		fetchUsers('id');
	}

	function toggleActiveStatus() {
		// Cycle through: All -> Active -> Inactive -> All
		if (selectedActiveStatus === 'All') {
			selectedActiveStatus = 'Active';
		} else if (selectedActiveStatus === 'Active') {
			selectedActiveStatus = 'Inactive';
		} else {
			selectedActiveStatus = 'All';
		}
		// Reset to page 1 and trigger server-side filtering
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	// ============== SEARCH FUNCTIONS ==============
	function delayedSearch() {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			fetchUsers();
		}, 500);
	}

	$: searchQuery, delayedSearch();

	// ============== SORTING FUNCTIONS ==============
	function sortBy(column: string) {
		if (sortColumn === column) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortColumn = column;
			sortDirection = 'asc';
		}
		handleServerSort(column);
	}

	function handleServerSort(column: string) {
		const field = columnFieldMap[column];
		if (field) {
			const ordering = sortDirection === 'desc' ? `-${field}` : field;
			currentOrdering = ordering;
			currentPage = 1;
			fetchUsers(ordering);
		}
	}

	// ============== PAGINATION FUNCTIONS ==============
	// $: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
	$: paginatedUsers = users;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		fetchUsers(currentOrdering);
	}

	// ============== SIDEBAR FUNCTIONS ==============
	function openSidebar(userId: number) {
		selectedUserId = userId;
		isSidebarOpen = true;
		// Pause real-time updates when sidebar is open to avoid conflicts
		pauseRealTimeTemporarily();
	}

	async function closeSidebar() {
		// Prevent closing if sidebar is loading
		if (isSidebarLoading) return;

		isSidebarOpen = false;
		selectedUserId = null;

		// Refresh the users table when sidebar closes to ensure data is up to date
		await fetchUsers(currentOrdering);
	}

	// Handle loading state changes from sidebar
	function handleSidebarLoadingStateChanged(event) {
		isSidebarLoading = event.detail.isLoading;
	}

	// Handle user updates from sidebar
	async function handleUserUpdated(event) {
		// Add a small delay to ensure server data is fully updated
		await new Promise(resolve => setTimeout(resolve, 300));
		
		// Refresh the users table data
		await fetchUsers(currentOrdering);
	}

	function handleUserRowClick(userId: number) {
		openSidebar(userId);
	}

	// ============== UTILITY FUNCTIONS ==============
	function formatTagName(tag: string): string {
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}

	$: userMatchedTags = users?.map((user) => ({
		id: user.id,
		tags: user.user_tags || []
	}));

	// ============== DEBUG FUNCTIONS ==============
	function toggleDebugMode() {
		debugMode = !debugMode;
		pollingService.setDebugMode(debugMode);
		console.log(`🐛 Debug mode ${debugMode ? 'enabled' : 'disabled'}`);
	}



	// ============== SHARED POLLING SERVICE FUNCTIONS ==============
	
	/**
	 * Unified data fetcher for polling service
	 * Wraps existing fetchUsers functionality for use with shared service
	 */
	async function fetchUsersForPolling(): Promise<any> {
		try {
			// Use current state for filters and ordering
			const statusFilters = Array.from(selectedStatuses).filter((s) => s !== 'All');
			const roleFilters = Array.from(selectedRoles).filter((r) => r !== 'All');
			const partnerFilters = Array.from(selectedPartners).filter((p) => p !== 'All');
			const departmentFilters = Array.from(selectedDepartments).filter((d) => d !== 'All');
			const specializedTagFilters = Array.from(selectedSpecializedTags).filter((t) => t !== 'All');

			// Map frontend active status values to backend expected values
			const userStatusFilter = selectedActiveStatus === 'All' ? 'all'
				: selectedActiveStatus === 'Active' ? 'active'
				: selectedActiveStatus === 'Inactive' ? 'inactive'
				: 'all';

			const filters = {
				search: searchQuery.trim() || '',
				status: statusFilters.join(','),
				role: roleFilters.join(','),
				partner: partnerFilters.join(','),
				department: departmentFilters.join(','),
				user_tag: specializedTagFilters.join(','),
				user_status: userStatusFilter,
				page: currentPage,
				page_size: itemsPerPage,
				_t: Date.now()
			};

			const response = await userService.getUsersWithFiltersAndOrdering(token, filters, currentOrdering);
			
			if (response.res_status === 200) {
				return response.users?.results || response.users || [];
			} else {
				throw new Error(response.error_msg || 'Failed to fetch users');
			}
		} catch (error) {
			console.error('fetchUsersForPolling error:', error);
			throw error;
		}
	}



	/**
	 * Handle real-time user updates from shared polling service
	 */
	function handleUsersUpdate(data: any): void {
		try {
			if (Array.isArray(data)) {
				// Always update users array with new data
				users = data;

				// Update pagination info if available
				if (data.length > 0) {
					totalItems = data.length;
					totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
				}

				// Update counter and indicators
				updateCounter++;
				hasNewUpdates = true;
				lastUpdateTime = new Date();

				if (debugMode) {
					console.log(`🔄 Users updated via shared polling service - Update #${updateCounter}`, {
						userCount: data.length,
						totalItems,
						totalPages
					});
				}

				// Clear update indicator after 3 seconds
				if (updateIndicatorTimeout) {
					clearTimeout(updateIndicatorTimeout);
				}
				updateIndicatorTimeout = setTimeout(() => {
					hasNewUpdates = false;
				}, 3000);
			}
		} catch (error) {
			console.error('handleUsersUpdate error:', error);
		}
	}

	/**
	 * Handle polling errors from shared service
	 */
	function handleUsersPollingError(error: Error): void {
		console.error('Users polling error:', error);
		connectionStatus = 'reconnecting';
		// Don't show UI errors for background polling to avoid spam
	}

	/**
	 * Start users polling using shared service
	 */
	function startUsersPolling(): void {
		try {
			if (!token) {
				console.warn('Cannot start users polling - no token available');
				return;
			}

			// Register users polling endpoint with the shared service using custom fetcher
			const success = pollingService.registerEndpoint('users-table', {
				interval: 5000, // 5 seconds to match existing behavior
				onDataChange: handleUsersUpdate,
				onError: handleUsersPollingError,
				debugMode: debugMode,
				customFetcher: fetchUsersForPolling // Use our custom data fetching function
			});

			if (success) {
				isUsersPollingEnabled = true;
				connectionStatus = 'connected';
				
				if (debugMode) {
					console.log('Users polling started with shared service using custom fetcher');
				}
			} else {
				console.error('Failed to start users polling with shared service');
			}
		} catch (error) {
			console.error('Error starting users polling:', error);
		}
	}

	/**
	 * Stop users polling
	 */
	function stopUsersPolling(): void {
		try {
			if (isUsersPollingEnabled) {
				pollingService.unregisterEndpoint('users-table');
				isUsersPollingEnabled = false;
				
				if (debugMode) {
					console.log('Users polling stopped');
				}
			}
		} catch (error) {
			console.error('Error stopping users polling:', error);
		}
	}

	// ============== LIFECYCLE ==============
	onMount(() => {
		// Initial data fetch
		fetchUsers();
		
		// Start shared polling service if real-time is enabled
		if (isRealTimeEnabled) {
			startUsersPolling();
		}
	});

	onDestroy(() => {
		// Stop shared polling service
		stopUsersPolling();
		
		// Clear any remaining timeouts
		if (updateIndicatorTimeout) {
			clearTimeout(updateIndicatorTimeout);
		}
		
		if (userInteractionTimeout) {
			clearTimeout(userInteractionTimeout);
		}
		
		// Clear snapshots on cleanup
		userDataSnapshot.clear();
	});
</script>

<svelte:head>
	<title>{t('users')}</title>
</svelte:head>

<div id="users-page-container" class="relative flex h-screen">
	<!-- Overlay for dimming effect when sidebar is open -->
	{#if isSidebarOpen}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div
			id="users-page-overlay"
			class="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 {isSidebarLoading ? 'cursor-not-allowed' : 'cursor-pointer'}"
			on:click={closeSidebar}
		></div>
	{/if}

	<div id="users-page-main" class="w-full overflow-y-auto bg-white p-8 transition-all duration-300 {isSidebarOpen ? 'md:mr-1/2' : ''}">
		<!-- <Breadcrumb id="users-page-breadcrumb" aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem id="users-page-breadcrumb-home" href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem id="users-page-breadcrumb-users">
				<span class="text-gray-700">{t('users')}</span>
			</BreadcrumbItem>
		</Breadcrumb> -->

		<!-- Enhanced Header with Real-time Status -->
		<div id="users-page-header" class="mb-6 flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
			<div class="flex-1">
				<div class="flex items-center gap-4 mb-2">
					<h2 id="users-page-title" class="text-2xl font-bold">{t('users_page_title')}</h2>
				</div>
				
				<div class="flex items-center gap-4">
					<p class="text-gray-600">{t('users_page_description')}</p>
				</div>
			</div>
			
			{#if data.role === 'Admin'}
				<div class="flex flex-nowrap gap-2 overflow-x-auto md:overflow-visible">
					<UserSignUp count={users?.length || 0} />
				</div>
			{/if}
		</div>

		<AdminStatistics users={all_users} />

		<!-- Enhanced Filters with Real-time Controls -->
		<!-- <div id="users-page-filters" class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between"> -->
		<div id="users-page-filters" class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
			<!-- Left side - Filter Buttons -->
			<div class="col-span-2">
				<div id="users-page-filter-buttons" class="flex flex-wrap gap-3">
					<!-- Status Filter -->
					<div>
						<Button
							id="users-page-status-filter-button"
							color={!selectedStatuses.has('All') ? 'dark' : 'none'}
							class={`${!selectedStatuses.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>{t('filter_status')}</span>
							<ChevronDownOutline class="h-3 w-3" />
						</Button>
						<Dropdown id="users-page-status-filter-dropdown" class="w-44 p-2 shadow-lg">
							{#each statusOptions as status}
								<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
									<Checkbox
										id="users-page-status-filter-{status.toLowerCase()}"
										checked={selectedStatuses.has(status)}
										on:change={() => toggleStatus(status)}
										class="text-gray-700 focus:ring-gray-700 p-2"
										inline
									/>
										<span class="ml-2 text-sm">
											{status === 'All'
												? t('filter_all')
												: t(status.toLowerCase())
											}
										</span>
								</Label>
							{/each}
						</Dropdown>
					</div>

					<!-- Role Filter -->
					<div>
						<Button
							id="users-page-role-filter-button"
							color={!selectedRoles.has('All') ? 'dark' : 'none'}
							class={`${!selectedRoles.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>{t('filter_role')}</span>
							<ChevronDownOutline class="h-3 w-3" />
						</Button>
						<Dropdown id="users-page-role-filter-dropdown" class="w-44 p-2 shadow-lg">
							{#each roleOptions as role}
								<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
									<Checkbox
										id="users-page-role-filter-{role.toLowerCase()}"
										checked={selectedRoles.has(role)}
										on:change={() => toggleRole(role)}
										class="text-gray-700 focus:ring-gray-700 p-2"
										inline
									/>
										<span class="ml-2 text-sm">
											{role === 'All'
												? t('filter_all')
												: t(role.toLowerCase())
											}
										</span>
								</Label>
							{/each}
						</Dropdown>
					</div>

					<!-- Partner Filter -->
					<div>
						<Button
							id="users-page-partner-filter-button"
							color={!selectedPartners.has('All') ? 'dark' : 'none'}
							class={`${!selectedPartners.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>{t('filter_partner')}</span>
							<ChevronDownOutline class="h-3 w-3" />
						</Button>
						<Dropdown id="users-page-partner-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
							{#each partnerOptions as partner}
								<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
									<Checkbox
										id="users-page-partner-filter-{partner.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
										checked={selectedPartners.has(partner.name)}
										on:change={() => togglePartner(partner)}
										class="text-gray-700 focus:ring-gray-700 p-2"
										inline
									/>
										<span class="ml-2 text-sm">
											{#if partner.name === 'All'}
												{partner.name}
											{:else}
												{formatTagName(partner.name)} ({partner.code})
											{/if}
										</span>
								</Label>
							{/each}
						</Dropdown>
					</div>

					<!-- Department Filter -->
					<div>
						<Button
							id="users-page-department-filter-button"
							color={!selectedDepartments.has('All') ? 'dark' : 'none'}
							class={`${!selectedDepartments.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>{t('filter_department')}</span>
							<ChevronDownOutline class="h-3 w-3" />
						</Button>
						<Dropdown id="users-page-department-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
							{#each departmentOptions as department}
								<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
									<Checkbox
										id="users-page-department-filter-{department.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
										checked={selectedDepartments.has(department.name)}
										on:change={() => toggleDepartment(department)}
									class="text-gray-700 focus:ring-gray-700 p-2"
									inline
								/>
									<span class="ml-2 text-sm">
										{#if department.name === 'All'}
											{department.name}
										{:else}
											{formatTagName(department.name)} ({department.code})
										{/if}
									</span>
								</Label>
							{/each}
						</Dropdown>
					</div>

					<!-- Specialized Tag Filter -->
					<div>
						<Button
							id="users-page-specialized-tag-filter-button"
							color={!selectedSpecializedTags.has('All') ? 'dark' : 'none'}
							class={`${!selectedSpecializedTags.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>{t('filter_specialized_tag')}</span>
							<ChevronDownOutline class="h-3 w-3" />
						</Button>
						<Dropdown id="users-page-specialized-tag-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
							{#each specializedTagOptions as tag}
								<Label class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100">
									<Checkbox
										id="users-page-specialized-tag-filter-{tag.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
										checked={selectedSpecializedTags.has(tag)}
										on:change={() => toggleSpecializedTag(tag)}
										class="text-gray-700 focus:ring-gray-700 p-2"
										inline
									/>
										<span class="ml-2 text-sm">
											{tag === 'All' ? tag : formatTagName(tag)}
										</span>
								</Label>
							{/each}
						</Dropdown>
					</div>

					<!-- Active Status Filter (Admin Only) -->
					{#if data.role === 'Admin'}
						<Button
							id="users-page-active-status-filter-button"
							color={selectedActiveStatus !== 'All' ? 'dark' : 'none'}
							on:click={toggleActiveStatus}
							class={`${selectedActiveStatus !== 'All' ? '' : 'border hover:bg-gray-100'} shadow-md`}
						>
							<AdjustmentsHorizontalSolid class="h-4 w-4 mr-1" />
							<span>
								{selectedActiveStatus === 'All'
									? t('filter_all_users')
									: selectedActiveStatus === 'Active'
										? t('filter_active_only')
										: t('filter_inactive_only')
								}
							</span>
						</Button>
					{/if}

					<!-- Reset Filter -->
					<Button
						id="users-page-reset-filters-button"
						color="none"
						on:click={resetFilters}
						class="w-auto border shadow-md hover:bg-gray-100"
					>
						{t('filter_reset')}
					</Button>
				</div>
			</div>

			<!-- Right side - Search Bar -->
			<div class="col-span-1">
				<div id="users-page-search-container" class="relative w-full shadow-md">
					<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
						<SearchOutline class="h-5 w-5 text-gray-500" />
					</div>
					<Input
						id="users-page-search-input"
						type="text"
						placeholder={t('search_user_placeholder')}
						bind:value={searchQuery}
						class={`block w-full rounded-lg border bg-white py-2.5 pl-10
							focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700
							${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
					/>
				</div>
			</div>
		</div>

		<!-- Enhanced Table with Better Change Indicators -->
		<Table id="users-page-table" shadow class="w-full table-fixed">
			<TableHead id="users-page-table-head">
				<TableHeadCell id="users-page-table-header-id" class="w-[60px]" on:click={() => sortBy('id')}>
					<div class="flex items-center justify-center">
						{t('table_no')}
						{#if sortColumn === 'id'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-status" class="w-[100px]" on:click={() => sortBy('status')}>
					<div class="flex items-center justify-center">
						{t('table_status')}
						{#if sortColumn === 'status'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-workload" class="w-[110px]" on:click={() => sortBy('workload')}>
					<div class="flex items-center justify-center">
						{t('table_workload')}
						{#if sortColumn === 'workload'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-name" class="w-[200px]" on:click={() => sortBy('name')}>
					<div class="flex items-left justify-start">
						{t('table_name')}
						{#if sortColumn === 'name'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-role" class="w-[100px]">{t('table_role')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-partner" class="w-[110px]">{t('table_partner')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-department" class="w-[110px]">{t('table_department')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-specialized-tag" class="w-[110px]">{t('table_specialize_tag')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-work-shift" class="w-[80px] text-center">{t('work_shift_column')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-last-active" class="w-[150px]" on:click={() => sortBy('last_active')}>
					<div class="flex items-center justify-start">
						{t('table_last_active')}
						{#if sortColumn === 'last_active'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
			</TableHead>
			<TableBody id="users-page-table-body">
				{#if paginatedUsers.length === 0}
					<TableBodyRow id="users-page-table-no-users-row">
						<TableBodyCell id="users-page-table-no-users-cell" colspan={10} class="py-4 text-center text-gray-500">
							{t('no_users')}
						</TableBodyCell>
					</TableBodyRow>
				{:else}
					{#each paginatedUsers as user}
						<!-- svelte-ignore a11y-click-events-have-key-events -->
						<!-- svelte-ignore a11y-no-static-element-interactions -->
						<TableBodyRow
							id="users-page-table-row-{user.id}"
							class="cursor-pointer hover:bg-gray-50 transition-colors duration-150 {selectedUserId === user.id ? 'bg-blue-50' : ''} {!user.is_active ? 'bg-gray-50' : ''}"
							on:click={() => handleUserRowClick(user.id)}
						>
							
							<TableBodyCell id="users-page-table-cell-id-{user.id}">
								<span
									id="users-page-user-{user.id}"
									class="flex items-center justify-center py-2 {!user.is_active ? 'text-gray-400' : ''}"
								>
									{user.id}
								</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-status-{user.id}">
								{#if user.is_active}
									<div class="flex justify-start items-center">
										<div id="users-page-user-status-{user.id}" class={`flex items-center gap-1 px-2 py-1 text-sm text-left rounded`}>
											<div
												class="h-2 w-2 rounded-full bg-{user.status === 'online' ? 'green' : user.status === 'busy' ? 'red' : user.status === 'away' ? 'yellow' : 'gray'}-400"
											/>
											<span class="text-{user.status === 'online' ? 'green' : user.status === 'busy' ? 'red' : user.status === 'away' ? 'yellow' : 'gray'}-600">{t(user.status)}</span>
										</div>
									</div>
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-workload-{user.id}">
								{#if user.is_active}
									<span id="users-page-user-workload-{user.id}" class="flex justify-center items-center">
										{user.current_workload}
									</span>
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-name-{user.id}">
								<span id="users-page-user-name-{user.id}" class="break-words align-middle inline-flex items-center gap-1 {!user.is_active ? 'text-gray-400' : ''}">
									{user.first_name} {user.last_name}
									{#if !user.is_active}
										<CircleMinusOutline 
											id="users-page-user-deactivated-{user.id}"
											size="sm"
										/>
										<Tooltip triggeredBy="#users-page-user-deactivated-{user.id}">
											<div class="flex items-center gap-2">
												{t('user.deactivated_badge')}
											</div>
										</Tooltip>
									{/if}
								</span>
								{#if data.role === 'Admin'}
									<div class="truncate text-xs {!user.is_active ? 'text-gray-400' : ''}">{user.username}</div>
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-role-{user.id}">
								<span id="users-page-user-role-{user.id}" class="{!user.is_active ? 'text-gray-400' : ''}">{t(user.roles.toLowerCase()) ? t(user.roles.toLowerCase()) : '-'}</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-partner-{user.id}">
								{#if user.is_active}
									{#if user.partners && user.partners.length > 0}
										{#if user.partners.length === 1}
											<span 
												class="text-white-700 inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-sm max-w-[100px] sm:max-w-[80px] md:max-w-[90px] lg:max-w-[100px]"
												data-popover-target="popover-partner-single-{user.id}"
											>
												<Indicator
													size="sm"
													class={`mr-1 ${getColorClass(user.partners[0].color)} flex-shrink-0`}
												/>
												<span class="truncate min-w-0">{user.partners[0].name}</span>
											</span>
											
											<Tooltip triggeredBy="[data-popover-target='popover-partner-single-{user.id}']">
												<div class="flex items-center gap-2">
													<Indicator
														size="sm"
														class={`${getColorClass(user.partners[0].color)}`}
													/>
													{user.partners[0].name}
												</div>
											</Tooltip>
										{:else}
											<div class="relative inline-block">
												<span
													class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
													data-popover-target="popover-partners-{user.id}"
												>
													<span class="flex items-center gap-1">
														<span class="relative flex -space-x-1">
															{#each user.partners.slice(0, 3) as partners, i (partners.name)}
																<Indicator
																	size="sm"
																	class={`${getColorClass(partners.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
														{user.partners.length}
														{t('labels')}
													</span>
												</span>

												<Tooltip triggeredBy="[data-popover-target='popover-partners-{user.id}']">
													<div class="max-w-xs px-2 py-1">
														<ul class="space-y-1">
															{#each user.partners as partner}
																<li class="flex items-center gap-1">
																	<Indicator
																		size="sm"
																		class={`mr-1 ${getColorClass(partner.color)}`}
																	/>
																	{partner.name}
																</li>
															{/each}
														</ul>
													</div>
												</Tooltip>
											</div>
										{/if}
									{/if}
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-department-{user.id}">
								{#if user.is_active}
									{#if user.departments && user.departments.length > 0}
										{#if user.departments.length === 1}
											<span 
												class="text-white-700 inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-sm max-w-[100px] sm:max-w-[80px] md:max-w-[90px] lg:max-w-[100px]"
												data-popover-target="popover-department-single-{user.id}"
											>
												<Indicator
													size="sm"
													class={`mr-1 ${getColorClass(user.departments[0].color)} flex-shrink-0`}
												/>
												<span class="truncate min-w-0">{formatTagName(user.departments[0].name)}</span>
											</span>
											
											<Tooltip triggeredBy="[data-popover-target='popover-department-single-{user.id}']">
												<div class="flex items-center gap-2">
													<Indicator
														size="sm"
														class={`${getColorClass(user.departments[0].color)}`}
													/>
													{formatTagName(user.departments[0].name)}
												</div>
											</Tooltip>
										{:else}
											<div class="relative inline-block">
												<span
													class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
													data-popover-target="popover-departments-{user.id}"
												>
													<span class="flex items-center gap-1">
														<span class="relative flex -space-x-1">
															{#each user.departments.slice(0, 3) as department, i (department.name)}
																<Indicator
																	size="sm"
																	class={`${getColorClass(department.color)}`}
																	style="z-index: {10 - i};"
																/>
															{/each}
														</span>
														{user.departments.length}
														{t('labels')}
													</span>
												</span>

												<Tooltip triggeredBy="[data-popover-target='popover-departments-{user.id}']">
													<div class="max-w-xs px-2 py-1">
														<ul class="space-y-1">
															{#each user.departments as department}
																<li class="flex items-center gap-1">
																	<Indicator
																		size="sm"
																		class={`mr-1 ${getColorClass(department.color)}`}
																	/>
																	{formatTagName(department.name)}
																</li>
															{/each}
														</ul>
													</div>
												</Tooltip>
											</div>
										{/if}
									{/if}
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-specialized-tag-{user.id}">
								{#if user.is_active}
									{#if user.user_tags && user.user_tags.length > 0}
										{#each userMatchedTags.filter((ut) => ut.id === user.id) as matched}
											{#if matched.tags && matched.tags.length > 0}
												{#if matched.tags.length === 1}
													<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
														<Indicator
															size="sm"
															class={`mr-1 ${getColorClass(matched.tags[0].color)} inline-block`}
														/>
														{formatTagName(matched.tags[0].name)}
													</span>
												{:else}
													<div class="relative inline-block">
														<span
															class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
															data-popover-target="popover-tags-{user.id}"
														>
															<span class="flex items-center gap-1">
																<span class="relative flex -space-x-1">
																	{#each matched.tags.slice(0, 3) as tag, i (tag.name)}
																		<Indicator
																			size="sm"
																			class={`${getColorClass(tag.color)}`}
																			style="z-index: {10 - i};"
																		/>
																	{/each}
																</span>
																{matched.tags.length}
																{t('labels')}
															</span>
														</span>

														<Tooltip triggeredBy="[data-popover-target='popover-tags-{user.id}']">
															<div class="max-w-xs px-2 py-1">
																<ul class="space-y-1">
																	{#each matched.tags as tag}
																		<li class="flex items-center gap-1">
																			<Indicator
																				size="sm"
																				class={`mr-1 ${getColorClass(tag.color)}`}
																			/>
																			{formatTagName(tag.name)}
																		</li>
																	{/each}
																</ul>
															</div>
														</Tooltip>
													</div>
												{/if}
											{:else}
												<span class="italic text-gray-500">{t('no_tags')}</span>
											{/if}
										{/each}
									{/if}
								{/if}
							</TableBodyCell>
			  				
			  				<TableBodyCell id="users-page-table-cell-work-shift-{user.id}">
								{#if user.is_active}
									{@const workSchedule = getUserWorkSchedule(user)}
									<div class="flex justify-center items-center gap-2">
										{#if workSchedule}
											<ClockOutline
												class="h-4 w-4 text-gray-500"
												data-popover-target="popover-workshift-{user.id}"
											/>
										{:else}
											<span class="text-gray-400 text-sm">{t('no_schedule_set')}</span>
										{/if}
									</div>

									{#if workSchedule}
										<Tooltip
											triggeredBy="[data-popover-target='popover-workshift-{user.id}']"
											class="z-50 border border-gray-200 bg-white shadow-lg"
											placement="bottom"
										>
											<div class="px-3 py-2">
												<!-- <div class="mb-2 font-semibold text-gray-900">
													{workSchedule.isBusinessHours ? t('business_hours') : t('weekly_schedule')}
												</div> -->
												<div class="space-y-2 text-sm">
													{#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
														<div class="flex min-w-[200px] flex-col">
															<div class="flex items-start justify-between">
																<span class="font-medium text-gray-900 min-w-[80px]">{t(dayKey)}:</span>
																<div class="text-right text-gray-700 flex-1 ml-2">
																	{#if String(time) === 'off'}
																		<span class="text-gray-500">{t('off')}</span>
																	{:else if String(time).includes(', ')}
																		{#each String(time).split(', ') as timeSlot}
																			<div class="leading-tight">
																				{timeSlot}
																			</div>
																		{/each}
																	{:else}
																		{String(time)}
																	{/if}
																</div>
															</div>
														</div>
													{/each}
												</div>
											</div>
										</Tooltip>
									{/if}
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-last-active-{user.id}">
								<div id="users-page-user-last-active-{user.id}" class="{!user.is_active ? 'text-gray-400' : ''}">
									<div>{displayDate(user.last_active).date}</div>
									<div>{displayDate(user.last_active).time}</div>
								</div>
							</TableBodyCell>
						</TableBodyRow>
					{/each}
				{/if}
			</TableBody>
		</Table>

		<!-- Pagination -->
		<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />
	</div>

	<!-- User Detail Sidebar -->
	<UserDetailSidebar
		bind:isOpen={isSidebarOpen}
		{selectedUserId}
		token={token || ''}
		on:closeSidebar={closeSidebar}
		on:userUpdated={handleUserUpdated}
		on:loadingStateChanged={handleSidebarLoadingStateChanged}
	/>
</div>

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>