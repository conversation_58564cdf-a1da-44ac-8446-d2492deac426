<script lang="ts">
    // import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
    import { t } from '$src/lib/stores/i18n';
    import { enhance } from '$app/forms';
    import {
        Dropdown,
        DropdownItem,
        Checkbox,
        Spinner,
        Input,
        Toggle,
        Label,
        Button,
        Drawer,
        CloseButton
    } from 'flowbite-svelte';
    import {
        EditSolid,
        ChevronDownOutline,
        TicketSolid,
        PhoneSolid,
        AdjustmentsHorizontalSolid,
        SearchOutline,
        InfoCircleSolid,
        ArrowRightOutline
    } from 'flowbite-svelte-icons';

    import type { PageData } from './$types';
    import { sineIn } from 'svelte/easing';
    import { TicketService } from '$src/lib/api/features/ticket/tickets.service';
    import TicketTable from '$src/lib/components/tickets/TicketTable.svelte';
    import TicketStatistics from '$src/lib/components/tickets/TicketStatistics.svelte';

    import {formatTimestamp, formatTimestampDMY } from '$lib/utils';
    import { onMount } from 'svelte';

    export let data: PageData;
    
    $: ({ tickets, users, statuses, error, priorities, ticket_topics, loginUser } = data);    

    // TODO - Replace name to selectedStatuses
    let selectedStatus = new Set(['All']);
    let selectedMainInterface = 'All';
    let selectedPriority = new Set(['All']);
    let selectedSentiment = new Set(['All']);

    const statusesName = ['All', 'open', 'assigned', 'waiting', 'closed']; //["default", "open", "close", "waiting to be assigned"]
    // TODO - Check this with Backend
    // const mainInterfaceOptions = ["All", "None", "Calling", "Line", "FBMessenger"];
    const mainInterfaceOptions = ['All', 'Undefine', 'LINE'];
    const priorityOptions = ['All', 'Immediately', 'High', 'Medium', 'Low'];
    const sentimentOptions = ['All', 'Positive', 'Neutral', 'Negative'];

    // Declare searchQuery
    let searchQuery = '';
    let viewMyTasks : boolean =  false;
    let ordering = 'status_id__id'; // default ordering (maps to backend field)
    let currentPage = 1;
    let itemsPerPage = 50;
    let totalPages = 1;
    let isLoading = false;

    // Map frontend column names to backend ordering field names
    const columnFieldMap = {
        'id': 'id',
        'status': 'status_id__id', 
        'priority': 'priority__id',
        'customer': 'customer_id__name',
        'agent': 'owner_id__name',
        'updated_ago': 'updated_on',  // Frontend displays 'updated_ago' but sorts by 'updated_on'
        'created_on': 'created_on',
        'sentiment': 'latest_sentiment'
    };

    function toggleViewMyTasks()  {
        viewMyTasks = !viewMyTasks;
        currentPage = 1; 
        fetchTickets();
        console.log('View My Tasks toggled:', viewMyTasks);
    }

    // Add a function to handle column header click
    function handleSort(column: string) {
        // Map frontend column name to backend field name
        const backendField = columnFieldMap[column];
        
        if (ordering === backendField) {
            ordering = '-' + backendField;
        } else if (ordering === '-' + backendField) {
            ordering = backendField;
        } else {
            ordering = backendField; // Default
        }
        
        currentPage = 1; // Reset to first page when sorting changes
        fetchTickets();
    }

    // Refactor ticket fetching to use ordering and filters
    const ticketService = new TicketService();

    async function fetchTickets() {
        if (isLoading) return; // To avoid multiple requests
        isLoading = true;
        
        // Build filters from current state
        const filters = {
            status_name: selectedStatus.has('All') ? '' : Array.from(selectedStatus).join(','),
            priority_name: selectedPriority.has('All') ? '' : Array.from(selectedPriority).join(','),
            sentiment: selectedSentiment.has('All') ? '' : Array.from(selectedSentiment).join(','),
            search: searchQuery.trim() || '',
            page: currentPage,
            my_tickets: viewMyTasks
        };

        console.log('=== FETCH TICKETS DEBUG ===');
        console.log('Current filters:', filters);
        console.log('Current ordering:', ordering);
        console.log('Current page:', currentPage);

        // Get token from data or session
        const token = data?.token || '';
        const response = await ticketService.getTicketsWithFiltersAndOrdering(token, filters, ordering);
        
        console.log('API Response status:', response.res_status);
        console.log('API Response data:', response);
        if (response.tickets?.results) {
            console.log('Number of tickets returned:', response.tickets.results.length);
            console.log('Total count:', response.tickets.count);
        }
        
        if (response.res_status === 200) {
            // Handle paginated response structure
            if (response.tickets.results) {
                tickets = response.tickets.results;
                // Backend appears to use 10 items per page based on actual response
                // Use this for consistent pagination calculation
                const backendPageSize = 10;
                const totalCount = response.tickets.count || 0;
                
                totalPages = Math.ceil(totalCount / backendPageSize);
                
                console.log('Calculated totalPages:', totalPages, 'from totalCount:', totalCount, 'and backendPageSize:', backendPageSize);
                console.log('Results length:', response.tickets.results.length);
                console.log('Has next:', !!response.tickets.next);
            } else {
                // Fallback for non-paginated response
                tickets = response.tickets;
                totalPages = 1;
            }
        } else {
            console.error('Failed to fetch tickets:', response.error_msg);
        }
        
        isLoading = false;
    }

    let pageTitle = 'Tickets';

    function togglePriority(priority) {
        console.log('=== TOGGLE PRIORITY ===');
        console.log('Priority clicked:', priority);
        console.log('Current selectedPriority before:', Array.from(selectedPriority));
        
        if (priority === 'All') {
            selectedPriority = new Set(['All']);
        } else {
            selectedPriority.delete('All');
            if (selectedPriority.has(priority)) {
                selectedPriority.delete(priority);
                if (selectedPriority.size === 0) {
                    selectedPriority.add('All');
                }
            } else {
                selectedPriority.add(priority);
            }
        }
        selectedPriority = new Set(selectedPriority); // Trigger reactivity
        console.log('Current selectedPriority after:', Array.from(selectedPriority));
        
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function toggleSentiment(sentiment) {
        if (sentiment === 'All') {
            selectedSentiment = new Set(['All']);
        } else {
            selectedSentiment.delete('All');
            if (selectedSentiment.has(sentiment)) {
                selectedSentiment.delete(sentiment);
                if (selectedSentiment.size === 0) {
                    selectedSentiment.add('All');
                }
            } else {
                selectedSentiment.add(sentiment);
            }
        }
        selectedSentiment = new Set(selectedSentiment); // Trigger reactivity
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function toggleStatus(status) {
        console.log('=== TOGGLE STATUS ===');
        console.log('Status clicked:', status);
        console.log('Current selectedStatus before:', Array.from(selectedStatus));
        
        if (status === 'All') {
            selectedStatus = new Set(['All']);
        } else {
            selectedStatus.delete('All');
            if (selectedStatus.has(status)) {
                selectedStatus.delete(status);
                if (selectedStatus.size === 0) {
                    selectedStatus.add('All');
                }
            } else {
                selectedStatus.add(status);
            }
        }
        selectedStatus = new Set(selectedStatus); // Trigger reactivity
        console.log('Current selectedStatus after:', Array.from(selectedStatus));
        
        currentPage = 1; // Reset to first page when filter changes
        fetchTickets(); 
    }

    function resetFilters() {
        selectedStatus = new Set(['All']);
        selectedMainInterface = 'All';
        selectedSentiment = new Set(['All']);
        selectedPriority = new Set(['All']);
        // searchQuery = '';
        viewMyTasks = false; // Reset view my tasks filter
        // ordering = 'status_id__id'; 
        currentPage = 1; // Reset to first page when filters are reset
        fetchTickets(); // Trigger fetch when filters are reset
    }

    function capitalize(str: string): string {
        return str
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    let hidden6 = true;
    let transitionParamsRight = {
        x: 320,
        duration: 200,
        easing: sineIn
    };

    // Add logic for date filtering and toggling between daily and total
    let dateFilter = 'total'; // Can be 'total' or 'daily'
    let selectedDate = new Date(); // Today's date for daily filter
    
    // Filter tickets by the selected date (for daily stats)
    function getDailyTickets() {
        return tickets.filter(ticket => {
            const ticketDate = new Date(ticket.date);
            return ticketDate.toDateString() === selectedDate.toDateString();
        });
    }

    // Filter tickets based on status (for total stats)
    function getTicketCount(status) {
        let ticketsToConsider = dateFilter === 'total' ? tickets : getDailyTickets();
        return ticketsToConsider.filter(ticket => ticket.status === status).length;
    }

    // Toggle between daily and total
    function toggleDateFilter() {
        dateFilter = dateFilter === 'total' ? 'daily' : 'total';
    }

    function updateCurrentPage(newCurrentPage: number) {
        currentPage = newCurrentPage;
        // Fetch new data for the selected page
        fetchTickets();
    }

    // Handle Enter key press in search box
    function handleSearchKeydown(event: KeyboardEvent) {
        if (event.key === 'Enter') {
            currentPage = 1; // Reset to first page when search is triggered manually
            fetchTickets();
        }
    }

    // Debounced search - trigger search after user stops typing for 500ms
    let searchTimeout: ReturnType<typeof setTimeout>;
    function delayedSearch() {
        if (searchTimeout) clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Reset to page 1 when search changes
            currentPage = 1;
            fetchTickets();
        }, 500);
    }
    
    $: searchQuery, delayedSearch();

    // Initialize data when component mounts
    onMount(() => {
        fetchTickets();
    });
</script>

<svelte:head>
    <title>{t('tickets')}</title>
</svelte:head>

<div id="tickets-page" class="flex h-screen bg-white" data-testid="tickets-page">
    <div id="tickets-content" class="flex-1 overflow-y-auto bg-white p-8" data-testid="tickets-content">
        <!-- <Breadcrumb id="tickets-breadcrumb" aria-label="Default breadcrumb example" class="mb-3" data-testid="tickets-breadcrumb">
            <BreadcrumbItem href="/" home>
            <span class="text-gray-400">{t('home')}</span>
            </BreadcrumbItem>
            <BreadcrumbItem>
            <span class="text-gray-700">{t('tickets')}</span>
            </BreadcrumbItem>
        </Breadcrumb> -->

        <div id="page-header" class="mb-6" data-testid="page-header">
            <div class="inline-flex flex-row gap-10 items-center">
                <span>
                    <h2 id="page-title" class="text-2xl font-bold mb-2" data-testid="page-title">{t('tickets')}</h2>
                    <p id="page-description" class="text-gray-600" data-testid="page-description">{t('tickets_description')}</p>
                </span>
                <Button
                    id="view-my-tickets-button"
                    color={viewMyTasks ? 'dark' : 'none'}
                    class={`${
                        viewMyTasks ? '' : 'hover:bg-gray-100 border'
                    } shadow-md`}
                    on:click={toggleViewMyTasks}
                    data-testid="view-my-tickets-button"
                >
                    {t('view_my_tickets')}
                </Button>
            </div>
        </div>
                
        
        <!-- Ticket Statistics Cards -->
        <!-- <TicketStatistics tickets={tickets} /> -->
        
        <!-- Filters and Search Bar - Improved Layout -->
        <div id="filters-search-container" class="flex flex-col lg:flex-row items-start lg:items-center lg:justify-between mb-6 gap-4" data-testid="filters-search-container">
            <!-- Left side - Filter Buttons -->
            <div id="filter-buttons" class="flex flex-wrap gap-3" data-testid="filter-buttons">
                <!-- Status Filter -->
                <div id="status-filter" data-testid="status-filter">
                    <Button 
                        id="status-filter-button"
                        color={!selectedStatus.has('All') ? 'dark' : 'none'}
                        class={`${!selectedStatus.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
                        data-testid="status-filter-button"
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4 mr-1" />
                        <span>{t('status')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown id="status-dropdown" class="w-44 p-2 shadow-lg" data-testid="status-dropdown">
                        {#each statusesName as statusName}
                            <Label id="status-option-{statusName.toLowerCase()}" class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100" data-testid="status-option">
                                <Checkbox
                                    id="status-checkbox-{statusName.toLowerCase()}"
                                    checked={selectedStatus.has(statusName)}
                                    on:change={() => toggleStatus(statusName)}
                                    data-testid="status-checkbox-{statusName.toLowerCase()}"
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                    <span class="ml-2 text-sm">
                                        <!-- {capitalize(statusName).replace('_', ' ')} -->
                                        {#if statusName === 'All'}
                                            {t('filter_all')}
                                        {:else}
                                            {t('tickets_' + statusName)}
                                        {/if}
                                    </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Priority Filter -->
                <div id="priority-filter" data-testid="priority-filter">
                    <Button
                        id="priority-filter-button"
                        color={!selectedPriority.has('All') ? 'dark' : 'none'}
                        class={`${!selectedPriority.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
                        data-testid="priority-filter-button"
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4 mr-1" />
                        <span>{t('priority')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown id="priority-dropdown" class="w-44 p-2 shadow-lg" data-testid="priority-dropdown">
                        {#each priorityOptions as priorityName}
                            <Label id="priority-option-{priorityName.toLowerCase()}" class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100" data-testid="priority-option">                     
                                <Checkbox 
                                    id="priority-checkbox-{priorityName.toLowerCase()}"
                                    checked={selectedPriority.has(priorityName)} 
                                    on:change={() => togglePriority(priorityName)}
                                    data-testid="priority-checkbox-{priorityName.toLowerCase()}"
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                    <!-- <span class="ml-2 text-sm">{priorityName.replace("_", " ")}</span> -->
                                     <span class="ml-2 text-sm">
                                        {#if priorityName === 'All'}
                                            {t('filter_all')}
                                        {:else}
                                            {t('tickets_priority_' + priorityName.toLowerCase())}
                                        {/if}
                                    </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Sentiment Filter -->
                <div id="sentiment-filter" data-testid="sentiment-filter">
                    <Button
                        id="sentiment-filter-button"
                        color={!selectedSentiment.has('All') ? 'dark' : 'none'}
                        class={`${!selectedSentiment.has('All') ? '' : 'hover:bg-gray-100 border'} shadow-md`}
                        data-testid="sentiment-filter-button"
                    >
                        <AdjustmentsHorizontalSolid class="w-4 h-4 mr-1" />
                        <span>{t('sentiment')}</span>
                        <ChevronDownOutline class="w-3 h-3" />
                    </Button>
                    <Dropdown id="sentiment-dropdown" class="w-44 p-2 shadow-lg" data-testid="sentiment-dropdown">
                        {#each sentimentOptions as sentimentName}
                            <Label id="sentiment-option-{sentimentName.toLowerCase()}" class="flex items-center text-sm font-medium text-gray-700 hover:bg-gray-100" data-testid="sentiment-option">
                                <Checkbox
                                    id="sentiment-checkbox-{sentimentName.toLowerCase()}"
                                    checked={selectedSentiment.has(sentimentName)}
                                    on:change={() => toggleSentiment(sentimentName)}
                                    data-testid="sentiment-checkbox-{sentimentName.toLowerCase()}"
                                    class="text-gray-700 focus:ring-gray-700 p-2"
                                    inline
                                />
                                    <!-- <span class="ml-2 text-sm">{sentimentName.replace('_', ' ')}</span> -->
                                    <span class="ml-2 text-sm">
                                        {#if sentimentName === 'All'}
                                            {t('filter_all')}
                                        {:else}
                                            {sentimentName.replace('_', ' ')}
                                        {/if}
                                    </span>
                            </Label>
                        {/each}
                    </Dropdown>
                </div>

                <!-- Reset Filter -->
                <Button 
                    id="reset-filters-button"
                    color="none" 
                    on:click={resetFilters}
                    class="hover:bg-gray-100 border shadow-md"
                    data-testid="reset-filters-button"
                >
                    {t('reset_filters')}
                </Button>
            </div>

            <!-- Right side - Search Bar -->
            <div id="search-container" class="relative w-full lg:w-1/3 shadow-md" data-testid="search-container">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <SearchOutline class="w-5 h-5 text-gray-500" />
                </div>
                <Input
                    id="search-input"
                    type="text"
                    placeholder={t('search_placeholder')}
                    bind:value={searchQuery}
                    on:keydown={handleSearchKeydown}
                    class={`bg-white block w-full pl-10 py-2.5 border rounded-lg
                        focus:outline-none focus:ring-2 focus:ring-blue-700 focus:border-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
                    data-testid="search-input"
                />
            </div>
        </div>
        
        <!-- Loading indicator
        {#if isLoading}
            <div class="flex justify-center items-center py-8">
                <Spinner class="mr-3" size="4" />
                <span>Loading tickets...</span>
            </div>
        {/if} -->
        
        <!-- Ticket Table Component -->
        <div id="ticket-table-container" data-testid="ticket-table-container">
            <TicketTable 
                tickets={tickets}
                {users}
                {statuses}
                {priorities}
                {ticket_topics}
                {loginUser}
                {currentPage}
                {totalPages}
                {updateCurrentPage}
                {ordering}
                {handleSort}
                on:ticketRefresh={fetchTickets}
            />
        </div>
    </div>
</div>